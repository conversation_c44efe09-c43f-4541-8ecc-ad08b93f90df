using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class CompanyPlugin(IElasticsearchService elasticsearchService)
{
    private readonly IElasticsearchService _elasticsearchService = elasticsearchService;

    [Description("Returns full company names for given NYC Real Estate company names")]
    public async Task<string[]> GetCompanies(
        [Description("A list of NYC Real Estate company names, such as 'Corcoran', 'Elliman', 'BHS', 'Halsteat', etc.")]
        List<string> companyNames,
        CancellationToken cancellationToken = default)
    {
        var searchTasks = companyNames
            .Select(companyName => _elasticsearchService.SearchCompaniesAsync(companyName, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var companies = searchResults
            .SelectMany(result => result)
            .Select(company => company.CompanyName)
            .ToArray();

        return companies;
    }
}
