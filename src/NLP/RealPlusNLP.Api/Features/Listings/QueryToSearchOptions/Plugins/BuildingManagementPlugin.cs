using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class BuildingManagementPlugin(IElasticsearchService elasticsearchService)
{
    private readonly IElasticsearchService _elasticsearchService = elasticsearchService;

    public async Task<BuildingManagementModel> GetBuildingManagement(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        return await _elasticsearchService.SearchBuildingManagementAsync(searchText, cancellationToken);
    }
}
