using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class BuildingManagementPlugin(IElasticsearchService elasticsearchService)
{
    [Description("Returns the building management companies/owners/landlords in the New York City for given names")]
    public async Task<BuildingManagementModel> GetBuildingManagement(
        [Description("A list of the building management company/owner/landlord names in the New York City")]
        List<string> buildingManagementNames,
        CancellationToken cancellationToken = default)
    {
        var searchTasks = buildingManagementNames
            .Select(management => elasticsearchService.SearchBuildingManagementAsync(management, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var resultCompanies = searchResults
            .SelectMany(result => result.Companies)
            .Distinct()
            .ToArray();
        var resultOwners = searchResults
            .SelectMany(result => result.Owners)
            .Distinct()
            .ToArray();
        var resultOwners = searchResults
            .SelectMany(result => result.Owners)
            .Distinct()
            .ToArray();

        return new BuildingManagementModel(resultCompanies, Array.Empty<string>(), Array.Empty<string>());

    }
}
