using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class SchoolPlugin(IElasticsearchService elasticsearchService)
{
    private readonly IElasticsearchService _elasticsearchService = elasticsearchService;

    [Description("Returns the school ids for given school names in the New York City")]
    public async Task<SchoolModel[]> GetSchools(
        [Description("A list of school names in the New York City, such as 'PS 333', 'MS 222', etc.")]
        List<string> schoolNames,
        CancellationToken cancellationToken = default)
    {
        var searchTasks = schoolNames
            .Select(schoolName => _elasticsearchService.SearchSchoolsAsync(schoolName, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var schools = searchResults
            .SelectMany(result => result)
            .Select(school => new SchoolModel(school.Id, school.ToString()))
            .ToArray();

        return schools;
    }
}
