using Microsoft.Extensions.Options;
using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Common.Models;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class ElasticsearchService(
    IElasticClient client,
    IOptions<ElasticsearchOptions> options)
    : IElasticsearchService
{
    private readonly IElasticClient _client = client;
    private readonly ElasticsearchOptions _options = options.Value;

    public async Task<IReadOnlyCollection<BuildingModel>> SearchBuildingsAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        static QueryContainer Prefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.Prefix(p => p.Field(f => f.BuildingName).Value(searchParam));
        }

        static QueryContainer MatchPhrasePrefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.MatchPhrasePrefix(p => p.Field(f => f.BuildingName).Query(searchParam));
        }

        var response = await _client.SearchAsync<PropertyDocument>(s => s
            .Index($"resource-{_options.BuildingIndexName}")
            .Size(1000)
            .Source(ss => ss.Includes(o => o.Fields(f => f.BuildingName, f => f.RPBin)))
            .Query(q => q.Bool(
                b => b.Should(
                    should => MatchPhrasePrefix(searchText, should),
                    should => Prefix(searchText, should)
            )))
            , cancellationToken);

        return response.Documents.GroupBy(document => document.BuildingName.ToUpper())
            .Select(groupItem => new BuildingModel
            (
                groupItem.Key,
                [.. groupItem.Select(item => item.RPBin ?? 0).OrderBy(item => item)]
            )).Take(10).ToList().AsReadOnly();
    }

    public async Task<IReadOnlyCollection<SchoolDocument>> SearchSchoolsAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        var searchString = searchText.ToLower();

        var response = await _client.SearchAsync<SchoolDocument>(sd => sd
            .Size(10)
            .Query(q => q
                .Bool(b => b
                    .Must(
                        m => m.Term(e => e.Field(f => f.HasZone).Value(true)),
                        s => s.Bool(bb => bb.Should(
                            ss => ss.Match(o => o.Field(f => f.Name).Query(searchString)),
                            ss => ss.Match(o => o.Field(f => f.Name.Suffix("classic"))
                                .Query(searchString).Operator(Operator.And)))))))
            , cancellationToken);

        return response.Documents.ToList().AsReadOnly();
    }

    public async Task<IReadOnlyCollection<CompanyDocument>> SearchCompaniesAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        var searchString = searchText.ToLower();

        var response = await _client.SearchAsync<CompanyDocument>(sd => sd
            .Index($"resource-{_options.CompanyIndexName}")
            .Size(10)
            .Query(q => q
                .Bool(b => b
                    .Should(
                        ss => ss.Prefix(p => p.Field(f => f.CompanyCode).Value(searchString)),
                        ss => ss.Prefix(p => p.Field(f => f.CompanyName).Value(searchString)),
                        ss => ss.MatchPhrasePrefix(p => p.Field(f => f.CompanyName)
                            .Query(searchString)))))
            , cancellationToken);

        return response.Documents.ToList().AsReadOnly();
    }
}
